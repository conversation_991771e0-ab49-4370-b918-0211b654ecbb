import * as fs from 'fs-extra';
import * as path from 'path';
import _ from 'lodash';
import type { GraphQLField } from 'graphql';
import Handlebars from 'handlebars';
import { RESOLVER_TEMPLATE } from '@templates/resolver-template';
import {
  convertToTypeScriptReturnType,
  formatArgs,
  generateDefaultReturnValue,
  calculateRelativeImportPath,
} from '@generators/utils/type-utils';
import { isScalarTypeName } from '@utils/type-converters';
import {
  hasCustomImplementation,
  preserveCustomImplementation,
} from '@generators/utils/file-utils';
import { makeSafeJavaScriptName } from '@generators/utils/keyword-utils';
import { DirectiveParser, type DirectiveContainer, type DefaultDirectiveParams } from '@utils/directive-parser';
import { DirectiveProcessor } from '@utils/directive-processor';
import { DecoratorProcessor } from '@utils/decorator-processor';
import { TypeDirectiveHandler } from '@resolvers/type-directive-handler';
import { InterfaceInheritanceHandler } from '@utils/interface-inheritance';
import { WatchedFileWriter } from '../../utils/watched-file-writer';
import { TemplateSanitizer } from '../../utils/template-sanitizer';
import { getGlobalTemplateCache } from '../../utils/template-compilation-cache';
import { getGlobalBatchFileWriter } from '../../utils/batch-file-writer';

// Register a safe helper for raw content (now redundant since escaping is disabled, but kept for compatibility)
Handlebars.registerHelper('safeRaw', function(content) {
  // This helper ensures content is treated as safe HTML/text
  return new Handlebars.SafeString(content || '');
});

// Get the global template cache for optimized compilation
const templateCache = getGlobalTemplateCache({
  enableLogging: false, // Disable logging in production generators
  enablePerformanceTracking: true,
});

// Configure cached template compilation with escaping disabled for TypeScript code generation
const compile = (template: string) => templateCache.compile(template, { noEscape: true });

/**
 * Extract scalar type names from a TypeScript return type annotation
 * @param returnTypeAnnotation The TypeScript return type string
 * @returns Array of scalar type names found in the return type
 */
function extractScalarTypesFromReturnType(returnTypeAnnotation: string): string[] {
  const scalarTypes: string[] = [];

  // Remove nullable unions and array wrappers to get base types
  const cleanType = returnTypeAnnotation
    .replace(/\s*\|\s*null/g, '')
    .replace(/Array<(.+?)>/g, '$1')
    .trim();

  // Split by union types if any
  const types = cleanType.split(/\s*\|\s*/);

  for (const type of types) {
    const trimmedType = type.trim();
    // Check if this type is a scalar type using our scalar mappings
    if (isScalarTypeName(trimmedType)) {
      scalarTypes.push(trimmedType);
    }
  }

  return [...new Set(scalarTypes)]; // Remove duplicates
}

/**
 * Generator for field resolvers
 */
export class FieldResolverGenerator {
  private preservedFiles: Set<string>;
  private processedFiles: Set<string>;
  private processedDirectories: Set<string>;
  private generatedFileTimestamps: Map<string, number>;
  private outputRoot: string;
  private force: boolean;
  private contextPath: string;
  private contextName: string;
  private schemaMapper: any;
  private typeDirectiveHandler: TypeDirectiveHandler;
  private interfaceInheritanceHandler: InterfaceInheritanceHandler;
  private decoratorDirectiveProvider?: (typeName: string, fieldName?: string, schemaId?: string) => DirectiveContainer;
  private decoratorSmartImportProvider?: (typeName: string, fieldName: string | undefined, targetOutputPath: string, schemaId?: string) => string[];
  private decoratorMetadataProvider?: () => any;
  private codebaseDir?: string;
  private useBatchWriter: boolean;
  private globalSchemaId?: string;

  /**
   * Create a new field resolver generator
   * @param schema The GraphQL schema
   * @param options Configuration options
   */
  constructor(schema: any, options: {
    outputRoot: string;
    force: boolean;
    contextPath: string;
    contextName: string;
    preservedFiles: Set<string>;
    processedFiles: Set<string>;
    processedDirectories: Set<string>;
    generatedFileTimestamps: Map<string, number>;
    schemaMapper: any;
    codebaseDir?: string;
  }) {
    this.outputRoot = options.outputRoot;
    this.force = options.force;
    this.contextPath = options.contextPath;
    this.contextName = options.contextName;
    this.preservedFiles = options.preservedFiles;
    this.processedFiles = options.processedFiles;
    this.processedDirectories = options.processedDirectories;
    this.generatedFileTimestamps = options.generatedFileTimestamps;
    this.schemaMapper = options.schemaMapper;
    this.codebaseDir = options.codebaseDir;
    this.typeDirectiveHandler = new TypeDirectiveHandler(this.schemaMapper.schemaRoot);
    this.interfaceInheritanceHandler = new InterfaceInheritanceHandler(schema, this.schemaMapper);

    // PERFORMANCE FIX: Disable batch writing as it's causing 20+ second delays
    // The batch writer has performance issues that need to be resolved
    this.useBatchWriter = false; // process.env.ENABLE_BATCH_IO !== 'false';
  }

  /**
   * Set decorator directive providers for integration with decorator system
   * @param directiveProvider Function to get decorator directives for a type/field
   * @param smartImportProvider Function to get smart imports for decorator method calls
   * @param metadataProvider Function to get decorator metadata for schema identifier extraction
   */
  public setDecoratorProviders(
    directiveProvider: (typeName: string, fieldName?: string, schemaId?: string) => DirectiveContainer,
    smartImportProvider: (typeName: string, fieldName: string | undefined, targetOutputPath: string, schemaId?: string) => string[],
    metadataProvider?: () => any
  ): void {
    this.decoratorDirectiveProvider = directiveProvider;
    this.decoratorSmartImportProvider = smartImportProvider;
    this.decoratorMetadataProvider = metadataProvider;
  }

  /**
   * Set the global schema identifier for multi-schema support
   * @param schemaId The global schema identifier
   */
  public setGlobalSchemaId(schemaId?: string): void {
    this.globalSchemaId = schemaId;
  }

  /**
   * Write file using batch writer or direct writing based on configuration
   * @private
   */
  private writeFile(filePath: string, content: string): void {
    if (this.useBatchWriter) {
      const batchWriter = getGlobalBatchFileWriter({
        enableLogging: process.env.DEBUG_PARSER === 'true',
        enablePerformanceMonitoring: true,
      });
      batchWriter.queueWrite(filePath, content);
    } else {
      WatchedFileWriter.writeFileSync(filePath, content);
    }
  }

  /**
   * Set alias configuration for import path generation
   * @param codebaseDir The codebase directory path
   * @param aliasCodebase The alias for codebase imports
   */
  public setAliasConfig(codebaseDir?: string, aliasCodebase?: string): void {
    this.interfaceInheritanceHandler.setAliasConfig(codebaseDir, aliasCodebase);
  }

  /**
   * Generate a resolver for a field
   * @param typeName The name of the type
   * @param fieldName The name of the field
   * @param field The GraphQL field
   * @param outputFile The output file path
   */
  /**
   * Extract function names that are called in a resolver expression
   * @param resolverCall The resolver call string (e.g., "customResolveType(obj)")
   * @returns Array of function names used in the call
   */
  private extractFunctionNamesFromCall(resolverCall: string): string[] {
    const functionNames: string[] = [];

    // Match function calls like functionName(args) or object.method(args)
    const functionCallRegex = /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g;
    let match;

    while ((match = functionCallRegex.exec(resolverCall)) !== null) {
      functionNames.push(match[1]);
    }

    return functionNames;
  }

  /**
   * Extract schema identifier from decorator metadata for a specific type and field
   * @param typeName The GraphQL type name
   * @param fieldName The GraphQL field name
   * @returns Schema identifier or null if not found
   */
  private extractSchemaIdentifierFromDecorators(typeName: string, fieldName: string): string | null {
    if (!this.decoratorMetadataProvider) {
      return null;
    }

    const decoratorMetadata = this.decoratorMetadataProvider();
    if (!decoratorMetadata || !decoratorMetadata.methodCalls) {
      return null;
    }

    // Find the matching decorator for this type/field
    const matchingDecorator = decoratorMetadata.methodCalls.find((mc: any) => {
      return mc.data.type === typeName && mc.data.field === fieldName;
    });

    return matchingDecorator?.data.schema || null;
  }

  /**
   * Generate smart default method call pattern when no explicit call is defined
   * @param fieldName The GraphQL field name
   * @param hasArgs Whether the field has arguments
   * @param aliasedFunctionName Optional aliased function name to use instead of field name
   * @returns Smart default method call pattern
   */
  private generateSmartDefaultMethodCall(fieldName: string, hasArgs: boolean, aliasedFunctionName?: string): string {
    // Use aliased function name if provided, otherwise convert field name to camelCase
    const methodName = aliasedFunctionName ?? _.camelCase(fieldName);

    if (hasArgs) {
      return `${methodName}({context, args, obj})`;
    } else {
      return `${methodName}({context, obj})`;
    }
  }

  /**
   * Detect if there's a naming conflict between field name and imported function name
   * @param fieldName The GraphQL field name
   * @param importedFunctionNames Array of imported function names
   * @returns True if there's a conflict
   */
  private hasNamingConflict(fieldName: string, importedFunctionNames: string[]): boolean {
    const camelCaseFieldName = _.camelCase(fieldName);
    return importedFunctionNames.includes(camelCaseFieldName);
  }

  /**
   * Generate an alias for a function name to avoid conflicts
   * @param originalName The original function name
   * @param fieldName The GraphQL field name
   * @returns Aliased function name
   */
  private generateFunctionAlias(originalName: string, _fieldName: string): string {
    // Generate alias by appending "Func" to avoid conflict with field name
    return `${originalName}Func`;
  }

  /**
   * Update method call content to use aliased function names
   * @param methodCall The original method call string
   * @param aliasMap Map of original function names to their aliases
   * @returns Updated method call with aliased function names
   */
  private updateMethodCallWithAlias(methodCall: string, aliasMap: Map<string, string>): string {
    if (aliasMap.size === 0) {
      return methodCall;
    }

    let updatedMethodCall = methodCall;

    // Replace each function name with its alias
    for (const [originalName, aliasName] of aliasMap) {
      // Use a regex that matches the function name followed by an opening parenthesis
      // This ensures we only replace actual function calls, not variables or properties
      const functionCallRegex = new RegExp(`\\b${originalName}\\s*\\(`, 'g');
      updatedMethodCall = updatedMethodCall.replace(functionCallRegex, `${aliasName}(`);
    }

    return updatedMethodCall;
  }

  /**
   * Update import statement to include alias
   * @param importStatement The original import statement
   * @param originalName The original function name
   * @param aliasName The alias name
   * @returns Updated import statement with alias
   */
  private addAliasToImport(importStatement: string, originalName: string, aliasName: string): string {
    // Handle different import patterns

    // Pattern 1: import { functionName } from "path"
    const namedImportMatch = importStatement.match(/^import\s*\{\s*([^}]+)\s*\}\s*from\s*(['"][^'"]+['"])/);
    if (namedImportMatch) {
      const [, imports, path] = namedImportMatch;
      const updatedImports = imports.split(',').map(imp => {
        const trimmed = imp.trim();
        if (trimmed === originalName) {
          return `${originalName} as ${aliasName}`;
        }
        return trimmed;
      }).join(', ');
      return `import { ${updatedImports} } from ${path}`;
    }

    // Pattern 2: import functionName from "path" (default import)
    const defaultImportMatch = importStatement.match(/^import\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s+from\s*(['"][^'"]+['"])/);
    if (defaultImportMatch) {
      const [, importName, path] = defaultImportMatch;
      if (importName === originalName) {
        return `import { default as ${aliasName} } from ${path}`;
      }
    }

    // If we can't parse it, return original
    return importStatement;
  }

  /**
   * Extract function names from import statements
   * @param importStatements Array of import statements
   * @returns Array of imported function names
   */
  private extractImportedFunctionNames(importStatements: string[]): string[] {
    const functionNames: string[] = [];

    for (const importStatement of importStatements) {
      const names = this.extractImportedNames(importStatement);
      functionNames.push(...names);
    }

    return functionNames;
  }

  /**
   * Check if an import statement contains a specific function
   * @param importStatement The import statement to check
   * @param functionName The function name to look for
   * @returns True if the import contains the function
   */
  private importContainsFunction(importStatement: string, functionName: string): boolean {
    const importedNames = this.extractImportedNames(importStatement);
    return importedNames.includes(functionName);
  }

  /**
   * Extract imported names from an import specifier
   * @param specifier The import specifier (e.g., "{ name1, name2 }" or "defaultName" or "* as namespace")
   * @returns Array of imported names
   */
  private extractImportedNames(specifier: string): string[] {
    // Handle named imports: { name1, name2, name3 as alias }
    if (specifier.includes('{') && specifier.includes('}')) {
      const namedImportsMatch = specifier.match(/\{([^}]+)\}/);
      if (namedImportsMatch) {
        return namedImportsMatch[1]
          .split(',')
          .map(name => name.trim().split(' as ')[0].trim())
          .filter(name => name.length > 0);
      }
    }

    // Handle default imports or namespace imports
    if (specifier.includes('* as ')) {
      // Namespace import: extract the namespace name
      const namespaceMatch = specifier.match(/\*\s+as\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/);
      return namespaceMatch ? [namespaceMatch[1]] : [];
    }

    // Handle mixed imports: defaultName, { named1, named2 }
    if (specifier.includes(',')) {
      const parts = specifier.split(',');
      const names: string[] = [];

      parts.forEach(part => {
        const trimmedPart = part.trim();
        if (trimmedPart.includes('{') && trimmedPart.includes('}')) {
          // This is the named imports part
          const namedImportsMatch = trimmedPart.match(/\{([^}]+)\}/);
          if (namedImportsMatch) {
            names.push(...namedImportsMatch[1]
              .split(',')
              .map(name => name.trim().split(' as ')[0].trim())
              .filter(name => name.length > 0));
          }
        } else {
          // This is the default import part
          names.push(trimmedPart);
        }
      });

      return names;
    }

    // Handle simple default import
    return [specifier.trim()];
  }

  public async generateFieldResolver(
    typeName: string,
    fieldName: string,
    field: GraphQLField<any, any>,
    outputFile: string
  ): Promise<void> {

    // Extract schema info
    const typeLocation = this.schemaMapper.getTypeLocation(typeName);
    const schemaFilePath = typeLocation ? typeLocation.sourceFile : 'unknown';

    // Get absolute path to the schema file
    const absoluteSchemaPath = path.resolve(this.schemaMapper.schemaRoot, schemaFilePath);



    // Parse field-level directives from comments
    const fieldDirectives = await DirectiveParser.extractDirectivesFromSchema(
      absoluteSchemaPath,
      typeName,
      fieldName
    );



    // Get decorator-based directives if provider is available
    let decoratorDirectives: DirectiveContainer = {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {}
    };
    if (this.decoratorDirectiveProvider) {
      // Use the global schema ID if available, otherwise extract from decorator metadata
      const schemaIdentifier = this.globalSchemaId || this.extractSchemaIdentifierFromDecorators(typeName, fieldName);
      decoratorDirectives = this.decoratorDirectiveProvider(typeName, fieldName, schemaIdentifier || undefined);
      if (process.env.DEBUG_PARSER) {
        console.log(`Found ${decoratorDirectives.methodCalls.length} decorator method calls, ${decoratorDirectives.imports.length} decorator imports for ${typeName}.${fieldName}`);
      }
    }

    // Get type-level directives to find any type-level field directive fields
    const typeDirectives = await this.typeDirectiveHandler.getTypeDirectives(
      typeName,
      schemaFilePath
    );

    // Get the field's return type to check for return type directives
    const returnType = field.type.toString();

    // Extract the base return type name (remove ! and [] modifiers)
    const baseReturnType = returnType.replace(/[!\[\]]/g, '');

    // Get return type directives if the return type is a custom type (not a primitive)
    let returnTypeDirectives: DirectiveContainer = {
      imports: [],
      methodCalls: [],
      fieldFields: [],
      others: {}
    };
    if (baseReturnType !== 'String' && baseReturnType !== 'Int' && baseReturnType !== 'Float' &&
      baseReturnType !== 'Boolean' && baseReturnType !== 'ID') {

      // Find where the return type is defined
      const returnTypeLocation = this.schemaMapper.getTypeLocation(baseReturnType);
      if (returnTypeLocation) {
        const returnTypeSchemaPath = returnTypeLocation.sourceFile;
        const absoluteReturnTypeSchemaPath = path.resolve(this.schemaMapper.schemaRoot, returnTypeSchemaPath);

        // Parse directives from the return type's schema file
        returnTypeDirectives = await DirectiveParser.extractDirectivesFromSchema(
          absoluteReturnTypeSchemaPath,
          baseReturnType,
          undefined // No field name - we want type-level directives
        );

        if (process.env.DEBUG_PARSER) {
          console.log(`Found ${returnTypeDirectives.fieldFields.length} field directive fields from return type ${baseReturnType} in ${returnTypeSchemaPath}`);
        }
      }
    }

    // Check if this field has @default directive and parse its parameters
    const hasDefaultDirective =
      fieldDirectives.others?.default && fieldDirectives.others.default.length > 0;

    let defaultDirectiveParams: DefaultDirectiveParams | null = null;
    if (hasDefaultDirective) {
      const defaultDirective = fieldDirectives.others.default[0];
      defaultDirectiveParams = DirectiveParser.parseDefaultDirective(defaultDirective.content);
    }

    // Check if the field has any directives (including both comment-based and decorator-based)
    const hasDirectives =
      fieldDirectives.imports.length > 0 ||
      fieldDirectives.methodCalls.length > 0 ||
      Object.keys(fieldDirectives.others).length > 0 ||
      fieldDirectives.fieldFields.length > 0 ||
      decoratorDirectives.imports.length > 0 ||
      decoratorDirectives.methodCalls.length > 0 ||
      Object.keys(decoratorDirectives.others).length > 0 ||
      decoratorDirectives.fieldFields.length > 0;



    // Collect all field directive fields from parent type, field-level, and return type directives
    const allFieldDirectiveFields = [
      ...typeDirectives.fieldFields,
      ...fieldDirectives.fieldFields,
      ...returnTypeDirectives.fieldFields
    ];

    // Check if context info exists in the file and needs updating
    let needsContextUpdate = false;
    if (fs.existsSync(outputFile)) {
      const fileContent = fs.readFileSync(outputFile, 'utf8');
      // Check if the context import in the file doesn't match the current context path
      const contextImportRegex = new RegExp(
        `import\\s*{\\s*${this.contextName}\\s*}\\s*from\\s*['"]([^'"]+)['"]`
      );
      const contextMatch = fileContent.match(contextImportRegex);

      if (contextMatch && contextMatch[1] !== this.contextPath) {
        console.log(
          `Context path changed from ${contextMatch[1]} to ${this.contextPath} for ${outputFile}, forcing update`
        );
        needsContextUpdate = true;
      }
    }

    // If the file exists, force is not set, there are no directives, and context hasn't changed, check for custom implementation
    // When directives are present, always regenerate the file to ensure directive-based method calls are applied
    if (fs.existsSync(outputFile) && !this.force && !hasDirectives && !needsContextUpdate) {
      const hasCustomCode = await hasCustomImplementation(outputFile);

      if (hasCustomCode) {
        // Preserve file with custom implementation
        this.preservedFiles.add(outputFile);

        // Clean up any incorrect resolve-types imports from regular field resolvers
        let fileContent = fs.readFileSync(outputFile, 'utf8');

        // Check if this is a regular field resolver (not a __resolveType function)
        if (!fileContent.includes('__resolveType')) {
          // Remove any resolve-types imports that shouldn't be in regular field resolvers
          // This regex matches various import patterns for resolve-types
          const resolveTypesImportRegex = /import\s+{[^}]*}\s+from\s+['"][^'"]*resolve-types[^'"]*['"];?\s*\n?/g;
          const originalContent = fileContent;
          fileContent = fileContent.replace(resolveTypesImportRegex, '');

          // Also remove any unused type alias references that might remain
          const typeAliasRegex = /\b\w+ResolveType\b/g;
          const typeAliasMatches = fileContent.match(typeAliasRegex);
          if (typeAliasMatches) {
            if (process.env.DEBUG_PARSER) {
              console.log(`[DEBUG] Found potential unused type aliases in ${outputFile}: ${typeAliasMatches.join(', ')}`);
            }
          }

          if (fileContent !== originalContent) {
            if (process.env.DEBUG_PARSER) {
              console.log(`[DEBUG] Removed resolve-types import from regular field resolver: ${outputFile}`);
            }
            this.writeFile(outputFile, fileContent);
          }
        }

        // Add auto-generated comment to preserved file only if there are no directives
        // Files with directives (like @methodCall) are actively maintained and don't need preservation comments
        if (!hasDirectives && !fileContent.includes('THIS FILE IS AUTO-GENERATED BUT PRESERVED')) {
          const autoGeneratedComment =
            '// THIS FILE IS AUTO-GENERATED BUT PRESERVED DUE TO CUSTOM IMPLEMENTATION\n';
          fileContent = fileContent.replace(/^((?:\/\/.*\n)*)/m, `$1${autoGeneratedComment}`);
          if (process.env.DEBUG_PARSER) {
            console.log(`Added auto-generated comment to preserved file: ${outputFile}`);
          }
        }

        this.writeFile(outputFile, fileContent);
        return;
      }
    }

    // Prepare template data
    const template = compile(RESOLVER_TEMPLATE);
    const hasArgs = field.args && field.args.length > 0;

    // Use the same naming convention as GraphQL Code Generator for consistency
    // This ensures args types match exactly (e.g., imageURL stays as ImageURL, not ImageUrl)
    const capitalizedFieldName = this.generateGraphQLCodeGenCompatibleFieldName(fieldName);

    // Make the field name safe for JavaScript by handling reserved keywords
    const camelCaseFieldName = makeSafeJavaScriptName(_.camelCase(fieldName));

    // Determine the import path to the generated types
    const relativePathToGenerated = calculateRelativeImportPath(
      outputFile,
      'graphql.ts',
      this.outputRoot
    );

    // Calculate the relative import path for context if it's not an alias
    let importPathToContext = this.contextPath;
    if (!importPathToContext.startsWith('@') && !path.isAbsolute(importPathToContext)) {
      // For relative paths, calculate the correct path from this specific resolver file
      const absoluteContextPath = path.resolve(process.cwd(), importPathToContext);
      const resolverDir = path.dirname(outputFile);
      let relativeContextPath = path.relative(resolverDir, absoluteContextPath);

      // Convert backslashes to forward slashes for import statements
      relativeContextPath = relativeContextPath.replace(/\\/g, '/');

      // Ensure the path starts with ./ or ../
      if (!relativeContextPath.startsWith('.')) {
        relativeContextPath = './' + relativeContextPath;
      }

      importPathToContext = relativeContextPath;
    }

    // Check if the field has a @field directive that overrides the return type
    let returnTypeAnnotation = '';
    const extractedFieldTypes: string[] = [];
    let needsReturnTypeImport = false;

    // Check for hidden fields from decorator directives
    const hasHiddenField = decoratorDirectives.fieldFields.some(field => field.hidden);
    if (hasHiddenField) {
      if (process.env.DEBUG_PARSER) {
        console.log(`Skipping resolver generation for hidden field: ${typeName}.${fieldName}`);
      }
      return; // Don't generate resolver for hidden fields
    }

    if (fieldDirectives.fieldFields.length > 0) {
      // Use the first field directive field as the return type override
      const fieldDirectiveField = fieldDirectives.fieldFields[0];

      // Skip if this field is marked as hidden
      if (fieldDirectiveField.hidden) {
        if (process.env.DEBUG_PARSER) {
          console.log(`Skipping resolver generation for hidden field: ${typeName}.${fieldName}`);
        }
        return;
      }

      returnTypeAnnotation = fieldDirectiveField.type;

      // Check if we need to import this type
      if (fieldDirectiveField.importPath) {
        needsReturnTypeImport = true;
        extractedFieldTypes.push(fieldDirectiveField.type);
      }

      // Log that we're using a custom type from @field directive
      console.log(
        `Using custom type ${returnTypeAnnotation} from @field directive for ${typeName}.${fieldName}`
      );
    } else {
      // Process return type normally if no @field directive
      returnTypeAnnotation = convertToTypeScriptReturnType(returnType, undefined, this.schemaMapper.schemaRoot);

      // Check if we need to import the return type
      // TypeScript primitives, scalar types, and mapped scalar types don't need to be imported
      const isPrimitiveOrScalarType =
        returnTypeAnnotation === 'string' ||
        returnTypeAnnotation === 'number' ||
        returnTypeAnnotation === 'boolean' ||
        returnTypeAnnotation === 'null' ||
        returnTypeAnnotation === 'Date' ||
        returnTypeAnnotation === 'File' ||
        returnTypeAnnotation === 'string | null' ||
        returnTypeAnnotation === 'number | null' ||
        returnTypeAnnotation === 'boolean | null' ||
        returnTypeAnnotation === 'Date | null' ||
        returnTypeAnnotation === 'File | null' ||
        returnTypeAnnotation === 'string | number' ||
        returnTypeAnnotation === 'string | number | null' ||
        /^Record<.+>$/.test(returnTypeAnnotation) ||
        /^Record<.+> \| null$/.test(returnTypeAnnotation) ||
        // Scalar types use Scalars['TypeName']['output'] syntax and don't need separate imports
        /^Scalars\['.+'\]\['output'\]/.test(returnTypeAnnotation) ||
        /^Scalars\['.+'\]\['output'\] \| null$/.test(returnTypeAnnotation);

      // Extract actual types that need to be imported
      if (!isPrimitiveOrScalarType) {
        if (!returnTypeAnnotation.includes('Array<')) {
          // For direct non-array types, extract the base type without nullable
          const baseType = returnTypeAnnotation.replace(/ \| null$/, '');
          extractedFieldTypes.push(baseType);
        } else {
          // For array types, extract the inner type
          const match = returnTypeAnnotation.match(/Array<(.+?)>/);
          if (match && match[1]) {
            const innerTypeWithNullable = match[1];
            // Remove nullable union if present
            const innerType = innerTypeWithNullable.replace(/ \| null$/, '');

            // If it's not a primitive type, scalar type, or mapped scalar type, add it to imports
            if (
              innerType !== 'string' &&
              innerType !== 'number' &&
              innerType !== 'boolean' &&
              innerType !== 'null' &&
              innerType !== 'Date' &&
              innerType !== 'File' &&
              innerType !== 'string | number' &&
              !innerType.startsWith('Record<') &&
              // Don't import scalar types that use Scalars['TypeName']['output'] syntax
              !/^Scalars\['.+'\]\['output'\]/.test(innerType)
            ) {
              extractedFieldTypes.push(innerType);
            }
          }
        }

        needsReturnTypeImport = extractedFieldTypes.length > 0;
      }
    }

    // Extract field-level directive imports with transformed paths
    const fieldImports = DirectiveProcessor.extractTransformedImportStatements(
      fieldDirectives,
      absoluteSchemaPath,
      outputFile
    );

    // Get decorator-based imports using codebase directory as base
    const decoratorImports = this.codebaseDir
      ? DirectiveProcessor.extractTransformedDecoratorImportStatements(
          decoratorDirectives,
          this.codebaseDir,
          outputFile
        )
      : DirectiveProcessor.extractTransformedImportStatements(
          decoratorDirectives,
          absoluteSchemaPath,
          outputFile
        );

    // Get smart imports for decorator method calls
    let decoratorSmartImports: string[] = [];
    if (this.decoratorSmartImportProvider) {
      decoratorSmartImports = this.decoratorSmartImportProvider(typeName, fieldName, outputFile, this.globalSchemaId);
    }

    // Get type-level imports that apply to all fields with transformed paths
    // Filter type-level imports to only include those relevant to this field
    // Type-level @field directives should only apply to fields if they are not overridden by field-level directives
    // Type-level @import directives should always apply to all fields in the type
    const relevantTypeImports: string[] = [];

    // Include type-level @import directives, but exclude those used for interface @resolver directive
    if (typeDirectives.imports.length > 0) {
      // Check if this type has a @resolver directive (interface/union resolveType)
      const hasResolverDirective = typeDirectives.others.resolver && typeDirectives.others.resolver.length > 0;

      // If there's a @resolver directive, filter out imports that are used by the resolver
      let filteredTypeImports = typeDirectives.imports;

      if (hasResolverDirective) {
        // Extract the resolver call to identify which imports it uses
        const resolverCall = DirectiveProcessor.extractResolverCall(typeDirectives);

        if (resolverCall) {
          // Extract function names used in the resolver call
          const functionNamesInResolver = this.extractFunctionNamesFromCall(resolverCall);

          // Filter out imports that are used by the resolver directive
          filteredTypeImports = typeDirectives.imports.filter(importDirective => {
            const importData = DirectiveProcessor.processImportDirective(importDirective);
            if (!importData) return true; // Keep unknown imports

            // Check if any function from this import is used in the resolver
            const importedNames = this.extractImportedNames(importData.specifier);
            const isUsedByResolver = importedNames.some(name =>
              functionNamesInResolver.includes(name)
            );

            if (isUsedByResolver) {
              if (process.env.DEBUG_PARSER) {
                console.log(`Excluding interface resolver import from field resolver: ${importData.statement}`);
              }
              return false;
            }
            return true;
          });
        }
      }

      // Create a directive container with the filtered import directives
      const importOnlyDirectives = {
        imports: filteredTypeImports,
        methodCalls: [],
        fieldFields: [],
        others: {},
      };

      const typeImportStatements = DirectiveProcessor.extractTransformedImportStatements(
        importOnlyDirectives,
        absoluteSchemaPath,
        outputFile
      );
      relevantTypeImports.push(...typeImportStatements);
    }

    // Only include type-level @field imports if:
    // 1. This field doesn't have its own field directive AND
    // 2. The type-level field directive fields are actually used as part of the field's return type
    if (fieldDirectives.fieldFields.length === 0 && typeDirectives.fieldFields.length > 0) {
      // Check if any type-level field directive fields are needed for this field's return type
      const isTypeUsedInReturnType = (fieldType: string): boolean => {
        // If the return type directly references this type
        if (returnType === fieldType || returnTypeAnnotation === fieldType) {
          return true;
        }

        // If it's part of a complex return type (like Array<Type>)
        if (returnTypeAnnotation.includes(fieldType)) {
          return true;
        }

        return false;
      };

      // Filter type-level directives to only include those used in this field
      const relevantFieldFields = typeDirectives.fieldFields.filter(fieldField =>
        isTypeUsedInReturnType(fieldField.type)
      );

      // Only transform and import relevant type-level directives
      if (relevantFieldFields.length > 0) {
        // Create a filtered directive container with only the relevant field directive fields
        const filteredTypeDirectives = {
          imports: [], // Don't include imports here since we already processed them above
          methodCalls: [],
          fieldFields: relevantFieldFields,
          others: {},
        };

        const typeFieldImports = DirectiveProcessor.extractTransformedImportStatements(
          filteredTypeDirectives,
          absoluteSchemaPath,
          outputFile
        );
        relevantTypeImports.push(...typeFieldImports);
      }
    }

    // Extract method call directive with decorator precedence
    let directiveMethodCall = DirectiveProcessor.extractMethodCall(decoratorDirectives);

    // If no decorator method call, fall back to comment-based directives
    if (!directiveMethodCall) {
      directiveMethodCall = DirectiveProcessor.extractMethodCall(fieldDirectives);
    }

    // Extract schema identifier from decorator metadata
    const schemaIdentifier = this.extractSchemaIdentifierFromDecorators(typeName, fieldName);

    // Check for interface inheritance if no direct method call is found
    let inheritanceInfo = null;
    if (!directiveMethodCall) {
      // Log inheritance analysis for debugging (only if there are interfaces to inherit from)
      const allInterfaces = this.interfaceInheritanceHandler.getAllInterfacesRecursively(typeName);
      if (allInterfaces.length > 0) {
        const analysis = this.interfaceInheritanceHandler.getInheritanceAnalysis(typeName);
        if (analysis.inheritanceDepth > 0) {
          if (process.env.DEBUG_PARSER) {
            console.log(`🔍 Multi-deep inheritance available for ${typeName}: ${allInterfaces.length} interfaces, max depth ${analysis.inheritanceDepth}`);
          }
        }
      }
      inheritanceInfo = await this.interfaceInheritanceHandler.findInheritedMethodCall(typeName, fieldName);
      if (inheritanceInfo) {
        directiveMethodCall = inheritanceInfo.methodCall;
        if (process.env.DEBUG_PARSER) {
          console.log(`✓ Interface inheritance: ${typeName}.${fieldName} inherits method call from ${inheritanceInfo.interfaceName}: ${directiveMethodCall}`);
        }

        // Validate that the inheritance is appropriate (now supports multi-deep inheritance)
        const allInterfaces = this.interfaceInheritanceHandler.getAllInterfacesRecursively(typeName);
        if (!allInterfaces.includes(inheritanceInfo.interfaceName)) {
          console.warn(`⚠️  Warning: ${typeName} does not implement ${inheritanceInfo.interfaceName} (directly or through inheritance) but trying to inherit method call`);
        } else {
          // Log inheritance depth for debugging
          const analysis = this.interfaceInheritanceHandler.getInheritanceAnalysis(typeName);
          const depth = analysis.interfaceDepths[inheritanceInfo.interfaceName];
          if (depth > 0) {
            if (process.env.DEBUG_PARSER) {
              console.log(`   📊 Multi-deep inheritance detected: ${inheritanceInfo.interfaceName} at depth ${depth}`);
            }
          }
        }
      }
    }

    // Handle inherited interface imports if applicable
    let inheritedImports: string[] = [];
    let inheritedDirectivesInfo = null;

    if (inheritanceInfo) {
      // Get absolute path to the schema file for the interface
      const interfaceTypeLocation = this.schemaMapper.getTypeLocation(inheritanceInfo.interfaceName);
      if (interfaceTypeLocation) {
        const interfaceAbsoluteSchemaPath = path.resolve(
          this.schemaMapper.schemaRoot,
          interfaceTypeLocation.sourceFile
        );

        // Extract imports from the inherited interface directives
        inheritedImports = DirectiveProcessor.extractTransformedImportStatements(
          inheritanceInfo.directives,
          interfaceAbsoluteSchemaPath,
          outputFile
        );
      }
    } else {
      // Check for @import-only inheritance (when no methodCall inheritance exists)
      inheritedDirectivesInfo = await this.interfaceInheritanceHandler.findInheritedDirectives(typeName, fieldName);
      if (inheritedDirectivesInfo) {
        if (process.env.DEBUG_PARSER) {
          console.log(`✓ Interface directive inheritance: ${typeName}.${fieldName} inherits directives from ${inheritedDirectivesInfo.interfaceName}`);
        }

        // Get absolute path to the schema file for the interface
        const interfaceTypeLocation = this.schemaMapper.getTypeLocation(inheritedDirectivesInfo.interfaceName);
        if (interfaceTypeLocation) {
          const interfaceAbsoluteSchemaPath = path.resolve(
            this.schemaMapper.schemaRoot,
            interfaceTypeLocation.sourceFile
          );

          // Extract imports from the inherited interface directives
          inheritedImports = DirectiveProcessor.extractTransformedImportStatements(
            inheritedDirectivesInfo.directives,
            interfaceAbsoluteSchemaPath,
            outputFile
          );
        }

        // Validate that the inheritance is appropriate (now supports multi-deep inheritance)
        const allInterfaces = this.interfaceInheritanceHandler.getAllInterfacesRecursively(typeName);
        if (!allInterfaces.includes(inheritedDirectivesInfo.interfaceName)) {
          console.warn(`⚠️  Warning: ${typeName} does not implement ${inheritedDirectivesInfo.interfaceName} (directly or through inheritance) but trying to inherit directives`);
        } else {
          // Log inheritance depth for debugging
          const analysis = this.interfaceInheritanceHandler.getInheritanceAnalysis(typeName);
          const depth = analysis.interfaceDepths[inheritedDirectivesInfo.interfaceName];
          if (depth > 0) {
            if (process.env.DEBUG_PARSER) {
              console.log(`   📊 Multi-deep directive inheritance detected: ${inheritedDirectivesInfo.interfaceName} at depth ${depth}`);
            }
          }
        }
      }
    }

    // Combine all directive imports with enhanced deduplication
    const allImports = [
      ...relevantTypeImports,
      ...fieldImports,
      ...decoratorImports,
      ...decoratorSmartImports,
      ...inheritedImports
    ];

    // Use enhanced deduplication from DecoratorProcessor
    const rawDirectiveImports = DecoratorProcessor.deduplicateImports(allImports);

    // Sanitize directive imports for template safety
    const directiveImports = rawDirectiveImports.map(importStatement => {
      const sanitizationResult = TemplateSanitizer.sanitizeImportStatement(importStatement);
      if (!sanitizationResult.isSafe) {
        if (process.env.DEBUG_PARSER) {
          console.warn(`Unsafe import statement detected and removed: ${sanitizationResult.error}`);
        }
        return ''; // Remove unsafe imports
      }
      return sanitizationResult.sanitizedValue || importStatement;
    }).filter(imp => imp.length > 0); // Remove empty imports

    // Handle the case where a type might come from both the @field directive and GraphQL types
    // by adjusting the needsReturnTypeImport flag
    if (extractedFieldTypes.length > 0 && directiveImports.length > 0) {
      // Check if any of the extracted types are already imported from a directive
      for (const fieldType of extractedFieldTypes) {
        const hasDirectiveImport = directiveImports.some(
          imp =>
            imp.includes(`{ ${fieldType} }`) ||
            imp.includes(`{ ${fieldType},`) ||
            imp.includes(`, ${fieldType} }`) ||
            imp.includes(`, ${fieldType},`)
        );

        // If the type is already imported from a directive, don't import it again from GraphQL types
        if (hasDirectiveImport) {
          needsReturnTypeImport = false;
          // Keep the type in extractedFieldTypes but mark that we don't need to import it again
          console.log(
            `Type ${fieldType} already imported from directive, not adding from GraphQL types`
          );
        }
      }
    }

    // Filter out any primitive types and scalar types from the typesToImport
    // TypeScript primitives and scalar types should not be imported from graphql.ts
    const filteredFieldTypes = extractedFieldTypes.filter(defType => {
      // Check if this is a primitive type or union of primitives
      const isPrimitive =
        defType === 'string' ||
        defType === 'number' ||
        defType === 'boolean' ||
        defType === 'null' ||
        defType === 'Date' ||
        defType === 'File' ||
        // Handle union types like "string | number"
        /^(string|number|boolean|null|Date|File)(\s*\|\s*(string|number|boolean|null|Date|File))+$/.test(defType) ||
        // Handle Record types like "Record<string, any>"
        /^Record<.+>$/.test(defType) ||
        // Handle scalar types that use Scalars['TypeName']['output'] syntax
        /^Scalars\['.+'\]\['output'\]/.test(defType) ||
        // Handle clean scalar types (will be imported from scalars.ts instead)
        isScalarTypeName(defType);

      if (isPrimitive) {
        if (process.env.DEBUG_PARSER) {
          console.log(`Removing primitive/scalar type "${defType}" from GraphQL imports`);
        }
        return false;
      }
      return true;
    });

    // Remove duplicates from filteredFieldTypes before creating the import statement
    const uniqueFilteredFieldTypes = [...new Set(filteredFieldTypes)];

    // Generate the default return statement based on @default directive parameters
    const generateDefaultReturnStatement = (): string => {
      if (!hasDefaultDirective || !defaultDirectiveParams) {
        return generateDefaultReturnValue(returnType);
      }

      if (defaultDirectiveParams.directReturn && defaultDirectiveParams.fallback) {
        // Direct return mode: return fallback directly
        return `return ${defaultDirectiveParams.fallback};`;
      } else if (defaultDirectiveParams.fallback) {
        // Fallback mode: return obj?.fieldName ?? fallback
        return `return obj?.${fieldName} ?? ${defaultDirectiveParams.fallback};`;
      } else {
        // No fallback specified, use safe optional chaining
        return `return obj?.${fieldName};`;
      }
    };

    // Generate qualified name for testing purposes including full path
    // Extract the relative path from output root to get the directory structure
    const relativePath = path.relative(this.outputRoot, path.dirname(outputFile));
    const pathParts = relativePath.split(path.sep).filter(part => part && part !== '.');

    // Remove duplicate type names from path parts to avoid redundancy
    const typeNameLower = typeName.toLowerCase();
    const cleanedPathParts = pathParts.filter(part =>
      _.kebabCase(part) !== _.kebabCase(typeName) &&
      part.toLowerCase() !== typeNameLower
    );

    // Convert path parts to camelCase and join them, ensuring proper camelCase
    const pathPrefix = cleanedPathParts.map((part, index) => {
      const camelCased = _.camelCase(part);
      // First part should be lowercase, subsequent parts should be PascalCase
      return index === 0 ? camelCased : _.upperFirst(camelCased);
    }).join('');

    // Create the qualified name with full path: pathPrefixTypeNameFieldName (starting with lowercase)
    const typeNamePart = _.upperFirst(_.camelCase(typeName));
    const fullQualifiedName = `${pathPrefix}${typeNamePart}${capitalizedFieldName}`;
    // Ensure the qualified name always starts with lowercase
    const qualifiedName = fullQualifiedName.charAt(0).toLowerCase() + fullQualifiedName.slice(1);

    // Check if we need to import scalar types from scalars.ts
    const scalarTypesInReturn = extractScalarTypesFromReturnType(returnTypeAnnotation);
    const needsScalarsImport = scalarTypesInReturn.length > 0;

    // Calculate import path to scalars.ts
    const importPathToScalars = calculateRelativeImportPath(
      outputFile,
      'scalars.ts',
      this.outputRoot
    );

    // Generate smart default method call if no explicit method call is defined
    let smartDefaultMethodCall = null;
    let finalDirectiveImports = directiveImports;

    if (!directiveMethodCall) {
      // Extract function names from all imports to detect potential conflicts
      const importedFunctionNames = this.extractImportedFunctionNames(directiveImports);

      // Only generate smart default if there are imported functions that could be called
      if (importedFunctionNames.length > 0) {
        // Check if there's a naming conflict
        const hasConflict = this.hasNamingConflict(fieldName, importedFunctionNames);

        if (hasConflict) {
          // Generate alias for the conflicting function
          const originalFunctionName = _.camelCase(fieldName);
          const aliasedFunctionName = this.generateFunctionAlias(originalFunctionName, fieldName);

          // Update imports to include aliases
          finalDirectiveImports = directiveImports.map(importStatement => {
            if (this.importContainsFunction(importStatement, originalFunctionName)) {
              return this.addAliasToImport(importStatement, originalFunctionName, aliasedFunctionName);
            }
            return importStatement;
          });

          // Generate smart default with aliased function name
          smartDefaultMethodCall = this.generateSmartDefaultMethodCall(fieldName, hasArgs, aliasedFunctionName);

          if (process.env.DEBUG_PARSER) {
            console.log(`🔧 Naming conflict detected for ${typeName}.${fieldName}: using alias ${aliasedFunctionName}`);
          }
        } else {
          // Check if there's a function that matches the field name
          const expectedFunctionName = _.camelCase(fieldName);
          if (importedFunctionNames.includes(expectedFunctionName)) {
            // Generate smart default with the matching function
            smartDefaultMethodCall = this.generateSmartDefaultMethodCall(fieldName, hasArgs);
            if (process.env.DEBUG_PARSER) {
              console.log(`✅ Smart default enabled for ${typeName}.${fieldName}: ${smartDefaultMethodCall}`);
            }
          }
        }
      }
    }

    // Generate the implementation
    const resolverData = {
      typeName,
      fieldName,
      capitalizedFieldName,
      camelCaseFieldName,
      hasArgs,
      needsResolversImport: false,
      resolverName: camelCaseFieldName, // Already made safe for JavaScript keywords
      description: field.description ?? `Resolver for ${typeName}.${fieldName}`,
      resolverType: typeName === 'Query' ? 'query' : typeName === 'Mutation' ? 'mutation' : 'field',
      resolverSignature: `${typeName}.${fieldName}: ${returnType}`,
      schemaFilePath,
      schemaRepresentation: `type ${typeName} { ${fieldName}${formatArgs(field.args)}: ${returnType} }`,
      importPathToGenerated: relativePathToGenerated,
      importPathToContext: importPathToContext,
      contextName: this.contextName,
      returnType,
      returnTypeAnnotation,
      needsReturnTypeImport: needsReturnTypeImport && uniqueFilteredFieldTypes.length > 0,
      needsScalarsImport,
      scalarTypesToImport: scalarTypesInReturn.join(', '),
      importPathToScalars,
      typesToImport: uniqueFilteredFieldTypes.join(', '),
      defaultReturnStatement: generateDefaultReturnStatement(),
      // Add qualified name for testing
      qualifiedName,
      // Add directive-based fields (sanitized, with aliases if needed)
      directiveImports: finalDirectiveImports,
      directiveMethodCall: directiveMethodCall ? (() => {
        // Apply aliasing to explicit method calls to prevent recursion
        let processedMethodCall = directiveMethodCall;

        if (directiveMethodCall) {
          // Extract function names from the method call
          const functionNamesInCall = this.extractFunctionNamesFromCall(directiveMethodCall);
          const aliasMap = new Map<string, string>();
          let needsAliasing = false;

          // Check each function name for conflicts with the field name
          for (const functionName of functionNamesInCall) {
            if (this.hasNamingConflict(fieldName, [functionName])) {
              const aliasedFunctionName = this.generateFunctionAlias(functionName, fieldName);
              aliasMap.set(functionName, aliasedFunctionName);
              needsAliasing = true;

              if (process.env.DEBUG_PARSER) {
                console.log(`🔧 Explicit method call conflict detected for ${typeName}.${fieldName}: ${functionName} -> ${aliasedFunctionName}`);
              }
            }
          }

          // If aliasing is needed, update both imports and method call content
          if (needsAliasing) {
            // Update imports to include aliases (modify finalDirectiveImports in place)
            for (let i = 0; i < finalDirectiveImports.length; i++) {
              for (const [originalName, aliasName] of aliasMap) {
                if (this.importContainsFunction(finalDirectiveImports[i], originalName)) {
                  finalDirectiveImports[i] = this.addAliasToImport(finalDirectiveImports[i], originalName, aliasName);
                }
              }
            }

            // Update method call content to use aliases
            processedMethodCall = this.updateMethodCallWithAlias(directiveMethodCall, aliasMap);
          }
        }

        const sanitizationResult = TemplateSanitizer.sanitizeMethodCall(processedMethodCall);
        if (!sanitizationResult.isSafe) {
          if (process.env.DEBUG_PARSER) {
            console.warn(`Unsafe method call detected and removed: ${sanitizationResult.error}`);
          }
          return null; // Remove unsafe method calls
        }
        return sanitizationResult.sanitizedValue || processedMethodCall;
      })() : null,
      // Add flag to indicate if file has directives (for "Do not edit" comment)
      hasDirectives: hasDirectives || hasDefaultDirective,
      // Add flag to indicate if field has @default directive (for template logic)
      hasDefaultDirective,
      // Add smart default method call for when no explicit call is defined
      smartDefaultMethodCall,
      // Add field directive fields information for comments
      fieldDirectiveFields: allFieldDirectiveFields.length > 0 ? allFieldDirectiveFields : null,
      // Add interface inheritance information for comments
      inheritedFromInterface: inheritanceInfo ? inheritanceInfo.interfaceName : (inheritedDirectivesInfo ? inheritedDirectivesInfo.interfaceName : null),
      hasInheritedMethodCall: !!inheritanceInfo,
      hasInheritedDirectives: !!(inheritanceInfo || inheritedDirectivesInfo),
      // Add schema identifier for method call comments
      schemaIdentifier,
      // Explicitly prevent resolve-types imports for regular field resolvers
      shouldUseTypeAlias: false,
      typeAliasName: '',
      importPathToResolveTypes: '',
    };

    const content = template(resolverData);

    // If the file exists and no directives (except @default), try to preserve custom implementation
    if (fs.existsSync(outputFile) && !hasDirectives && !hasDefaultDirective) {
      if (process.env.DEBUG_PARSER) {
        console.log('Preserving custom implementation in', outputFile);
      }
      const mergedContent = await preserveCustomImplementation(outputFile, content);
      this.writeFile(outputFile, mergedContent);
    } else {
      // Create a new file or overwrite if directives exist
      this.writeFile(outputFile, content);
    }

    // Track the file we've processed
    // Ensure we store the absolute path for consistency
    const absoluteOutputFile = path.resolve(outputFile);
    this.processedFiles.add(absoluteOutputFile);
    this.processedDirectories.add(path.dirname(absoluteOutputFile));
    this.generatedFileTimestamps.set(absoluteOutputFile, Date.now());

    const actionVerb = hasDirectives || hasDefaultDirective ? 'Overwrote' : 'Generated';
    const directiveInfo = hasDefaultDirective
      ? ' (has @default directive)'
      : hasDirectives
        ? ' (has directives)'
        : '';
    if (process.env.DEBUG_PARSER) {
      console.log(
        `${actionVerb} resolver for ${typeName}.${fieldName} at ${outputFile}${directiveInfo}`
      );
    }
  }

  /**
   * Generate field name that's compatible with GraphQL Code Generator's naming convention
   * This ensures args types match exactly by using the same logic as the custom naming convention
   * @param fieldName The original field name
   * @returns Field name compatible with GraphQL Code Generator
   */
  private generateGraphQLCodeGenCompatibleFieldName(fieldName: string): string {
    if (!fieldName || fieldName.length === 0) {
      return fieldName;
    }

    // Use the same custom naming convention that GraphQL Code Generator uses
    // This ensures 100% compatibility with generated types
    try {
      const customNamingConvention = require('../../utils/custom-naming-convention');

      // For field names in args types, GraphQL Code Generator applies the naming convention
      // to the pattern: TypeName + FieldName + Args
      // We simulate this by applying the naming convention to just the field name part
      const simulatedArgTypeName = `Type${fieldName}Args`;
      const transformedArgTypeName = customNamingConvention(simulatedArgTypeName);

      // Extract the field name part from the transformed result
      const match = transformedArgTypeName.match(/^Type(.+)Args$/);
      if (match) {
        return match[1]; // Return the transformed field name part
      }

      // Fallback: capitalize first letter, preserve the rest
      return fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    } catch (error) {
      // Fallback if custom naming convention fails
      if (process.env.DEBUG_PARSER) {
        console.warn(`Failed to use custom naming convention for field ${fieldName}:`, error);
      }
      return fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    }
  }
}
